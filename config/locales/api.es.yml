es:
  api:
    v1:
    v2:
      contents:
        errors:
          save_error: "Ocurrió un error al guardar el contenido."
          max_limit: El límite máximo es 500.
          incorrect_parameters: Parámetros incorrectos para la solicitud.
          parent_id_required: El parent_id es obligatorio para subnegocios.
          parent_id_not_found: No se encontró ningún parent_id con ese GUID.
          content_not_found: Contenido no encontrado.
          field_validation:
            array_required: "El valor para el campo: '%{field_id}' debe ser un array"
            integer_required: "El valor para el campo: '%{field_id}' debe ser un número entero"
            decimal_required: "El valor para el campo: '%{field_id}' debe ser un número decimal"
            string_required: "El valor para el campo: '%{field_id}' debe ser un texto"
            date_required: "El valor para el campo: '%{field_id}' debe ser una fecha"
            value_not_provided: "El valor del campo '%{field_id}' no fue informado correctamente"
      users:
        errors:
          max_limit: El límite máximo es 500.
          user_not_found: Usuario no encontrado.
          unauthorized: No autorizado.
      administrators:
        errors:
          max_limit: El límite máximo es 500.
          administrator_not_found: Administrador no encontrado.
          unauthorized: No autorizado.
          update_name: No se puede editar el nombre de usuario administrador.
          email_already_exists: El email ya existe.
      departments:
        errors:
          max_limit: El límite máximo es 500.
          department_not_found: Departamento no encontrado.
          unauthorized: No autorizado.