pt-BR:
  api:
    v1:
    v2:
      contents:
        errors:
          save_error: "Ocorreu um erro ao salvar o conteúdo."
          max_limit: Limite máximo é 500.
          incorrect_parameters: Parâmetros incorretos para a requisição.
          parent_id_required: O parent_id é obrigatório para subnegócios.
          parent_id_not_found: Não foi encontrado nenhum parent_id com esse GUID.
          content_not_found: Conteúdo não encontrado.
          field_validation:
            array_required: "O valor para o campo: '%{field_id}' deve ser um array"
            integer_required: "O valor para o campo: '%{field_id}' deve ser um número inteiro"
            decimal_required: "O valor para o campo: '%{field_id}' deve ser um número decimal"
            string_required: "O valor para o campo: '%{field_id}' deve ser um texto"
            date_required: "O valor para o campo: '%{field_id}' deve ser uma data"
            value_not_provided: "Valor do campo '%{field_id}' não foi informado corretamente"
      users:
        errors:
          max_limit: Limite máximo é 500.
          user_not_found: Usuário não encontrado.
          unauthorized: Não autorizado.
      administrators:
        errors:
          max_limit: Limite máximo é 500.
          administrator_not_found: Adiministrador não encontrado.
          unauthorized: Não autorizado.
          update_name: Não foi possível editar o nome do usuário admin.
          email_already_exists: O email já existe.
      departments:
        errors:
          max_limit: Limite máximo é 500.
          department_not_found: Departamento não encontrado.
          unauthorized: Não autorizado.