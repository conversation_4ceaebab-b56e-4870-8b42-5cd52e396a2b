# :nocov:
module Overrides
  class OmniauthCallbacksController < DeviseTokenAuth::OmniauthCallbacksController

    ALLOWED_RESOURCES = ["User", "Administrator"].freeze

    def redirect_callbacks
      # derive target redirect route from 'resource_class' param, which was set
      # before authentication.
      devise_mapping = get_devise_mapping
      redirect_route = get_redirect_route(devise_mapping)

      # Preserve omniauth info for success route. Ignore 'extra' to avoid CookieOverflow.
      session['dta.omniauth.auth'] = request.env['omniauth.auth'].slice('info', 'provider', 'uid')
      session['dta.omniauth.params'] = request.env['omniauth.params']

      upn = request.env['omniauth.auth'].dig('extra', 'raw_info', 'upn')
      session['dta.omniauth.auth']['upn'] = upn if upn.present?

      emails = request.env['omniauth.auth'].dig('extra', 'raw_info', 'emails')
      session['dta.omniauth.auth']['info']['email'] = emails.first if emails.present?

      redirect_to redirect_route
    end

    def omniauth_success
      auth_hash['info']['email'] = (auth_hash.dig('extra', 'raw_info', 'upn').presence || auth_hash.dig('info', 'email'))&.downcase
      return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.no_email')) if auth_hash['info']['email'].blank?

      subdomain = URI.parse(omniauth_params['auth_origin_url']).host.split('.').first
      email_parts = auth_hash['info']['email'].split('@')
      email_name = email_parts.first
      email_domain = email_parts.last

      auth_hash['info']['name'] = auth_hash['info']['name'].presence || email_name
      auth_hash['uid'] = auth_hash['uid'].presence || auth_hash['info']['email']

      Apartment::Tenant.switch(subdomain) do
        return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.disabled_login_with_google')) if auth_hash['provider'] == 'google_oauth2' && !Company.current&.enable_google_oauth?
        return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.disabled_login_with_microsoft')) if auth_hash['provider'] == 'entra_id' && !Company.current&.enable_microsoft_oauth?
        return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.disabled_login_with_open_id')) if auth_hash['provider'] == 'openid_connect' && !Company.current&.enable_open_id?

        unless Company.current&.auth_domain&.include?(email_domain) || (auth_hash['provider'] == 'openid_connect' && Company.current&.not_validate_auth_domain_openid?)
          return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.disabled_domain'))
        end

        get_resource_from_auth_hash

        set_token_on_resource
        create_auth_params

        if confirmable_enabled?
          # don't send confirmation email!!!
          @resource.skip_confirmation!
        end

        sign_in(:user, @resource, store: false, bypass: false)

        @resource.approved = false if @oauth_registration
        if resource_class == User
          @resource.limited = Company.current.limit_user_on_signup?
        end
        @resource.save!

        return render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.user_waiting_for_approval')) unless @resource.approved?

        yield @resource if block_given?

        render_data_or_redirect('deliverCredentials', @auth_params.as_json, @resource.as_json)
      rescue StandardError => e
        render_data_or_redirect('authFailure', error: I18n.t('devise.omniauth.general', message: e.message))
      end
    end

    def get_resource_from_auth_hash
      # find or create user by provider and provider uid
      @resource = resource_class.where(
        email: auth_hash['info']['email'],
        provider: auth_hash['provider']
      ).first_or_initialize

      @resource.assign_attributes(
        uid: auth_hash['uid'],
      )

      if @resource.new_record?
        handle_new_resource
      end

      # sync user info with provider, update/generate auth token
      assign_provider_attrs(@resource, auth_hash)

      # assign any additional (whitelisted) attributes
      if assign_whitelisted_params?
        extra_params = whitelisted_params
        @resource.assign_attributes(extra_params) if extra_params
      end

      @resource
    end

    def resource_class(mapping = nil)
      resource_name = omniauth_params['resource_class'] || params['resource_class']

      if ALLOWED_RESOURCES.include?(resource_name)
        resource_name.safe_constantize
      else
        raise 'No resource_class found'
      end
    end
  end
end
# :nocov:
