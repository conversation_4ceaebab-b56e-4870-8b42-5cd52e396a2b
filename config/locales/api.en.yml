en:
  api:
    v1:
    v2:
      contents:
        errors:
          save_error: "An error occurred while saving the content."
          max_limit: Max limit is 500.
          incorrect_parameters: Incorrect parameters for the request.
          parent_id_required: Parent content id is required for sub business.
          parent_id_not_found: Parent content id not found.
          content_not_found: Content not found.
          field_validation:
            array_required: "The value for field: '%{field_id}' must be an array"
            integer_required: "The value for field: '%{field_id}' must be an integer"
            decimal_required: "The value for field: '%{field_id}' must be a decimal number"
            string_required: "The value for field: '%{field_id}' must be a string"
            date_required: "The value for field: '%{field_id}' must be a date"
            value_not_provided: "Value for field '%{field_id}' was not provided correctly"
      users:
        errors:
          max_limit: Max limit is 500.
          user_not_found: User not found.
          unauthorized: Unauthorized.
      administrators:
        errors:
          max_limit: Max limit is 500.
          administrator_not_found: Administrator not found.
          unauthorized: Unauthorized.
          update_name: Unable to edit admin user name.
          email_already_exists: Email already exists.
      departments:
        errors:
          max_limit: Max limit is 500.
          department_not_found: Department not found.
          unauthorized: Unauthorized.
